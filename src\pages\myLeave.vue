<route lang="json5">
{
  style: {
    navigationBarTitleText: '我的请假',
  },
}
</route>
<template>
  <view class="leave-page bg-gray-100 p-4">
    <!-- 请假统计 -->
    <view class="custom-card mb-4 bg-white rounded-lg overflow-hidden">
      <view class="card-header flex justify-between items-center p-3">
        <text class="card-title font-bold text-lg">请假统计</text>
      </view>
      <view class="p-3">
        <view class="grid grid-cols-3 gap-4 mb-4">
          <view class="flex flex-col items-center py-3 rounded-lg bg-blue-50">
            <text class="text-xl font-bold text-blue-500">
              {{ leaveStatistics?.statistics?.total || 0 }}
            </text>
            <text class="text-xs text-gray-500">累计请假</text>
          </view>
          <view class="flex flex-col items-center py-3 rounded-lg bg-green-50">
            <text class="text-xl font-bold text-green-500">
              {{ leaveStatistics?.statistics?.thisMonth || 0 }}
            </text>
            <text class="text-xs text-gray-500">本月请假</text>
          </view>
          <view class="flex flex-col items-center py-3 rounded-lg bg-yellow-50">
            <text class="text-xl font-bold text-yellow-500">
              {{ leaveStatistics?.statistics?.thisWeek || 0 }}
            </text>
            <text class="text-xs text-gray-500">本周请假</text>
          </view>
        </view>

        <view
          v-for="(item, index) in leaveStatistics?.leaveTypeData"
          :key="index"
          class="flex items-center justify-between mb-2"
          v-show="item.value > 0"
        >
          <view class="flex items-center">
            <view
              class="w-3 h-3 rounded-full mr-2"
              :style="{ backgroundColor: getLeaveTypeColorFromDict(item.name) }"
            ></view>
            <text class="text-xs text-gray-500">{{ item.name }}</text>
          </view>
          <text class="text-xs font-medium">{{ item.value }}次</text>
        </view>
      </view>
    </view>

    <!-- 请假记录 -->
    <view class="custom-card mb-4 bg-white rounded-lg overflow-hidden">
      <view class="card-header flex justify-between items-center p-3">
        <text class="card-title font-bold text-lg">请假记录</text>
        <!--        <view class="flex text-xs">
          <select
            class="bg-transparent text-blue-500 border-none outline-none"
            @change="handleStatusChange($event)"
          >
            <option v-for="(option, index) in statusOptions" :key="index" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </view>-->
      </view>
      <view v-if="loading" class="p-8 text-center">
        <view class="loading-spinner"></view>
        <text class="text-gray-500 mt-2">加载中...</text>
      </view>
      <view v-else-if="leaveRecords.length === 0" class="p-8 text-center">
        <text class="text-gray-500">暂无请假记录</text>
      </view>
      <view v-else class="leave-list overflow-hidden">
        <view
          v-for="record in leaveRecords"
          :key="record.id"
          class="p-3 border-b border-gray-100"
          @click="viewLeaveDetail(record.id)"
        >
          <view class="flex items-start">
            <view
              class="w-10 h-10 rounded-lg flex items-center justify-center mr-3 flex-shrink-0"
              :style="{
                backgroundColor: getLeaveTypeIconBgColor(record.qjlxmc),
                color: getLeaveTypeIconColor(record.qjlxmc),
              }"
            >
              <wd-icon
                :name="getLeaveTypeIcon(record.qjlxmc)"
                :color="getLeaveTypeIconColor(record.qjlxmc)"
                size="20px"
              />
            </view>
            <view class="flex-1">
              <view class="flex justify-between items-start">
                <view class="flex flex-col">
                  <text class="font-medium">
                    {{ getLeaveTypeLabel(record.qjlx) || record.qjlxmc }}
                    <!-- 添加请假类别显示 -->
                    <text v-if="getLeaveCategoryLabel(record.wqsq)" class="ml-1">
                      - {{ getLeaveCategoryLabel(record.wqsq) }}
                    </text>
                  </text>
                </view>
                <StatusTag
                  :text="getApprovalStatus(record).status"
                  :type="getApprovalStatus(record).color"
                />
              </view>
              <view class="flex flex-wrap text-xs text-gray-500 mt-1">
                <view class="w-full mb-1">请假时间：{{ record.leaveTime }}</view>
                <view class="w-full">请假原因：{{ record.qjsy }}</view>
              </view>
              <view class="flex justify-between mt-2">
                <text class="text-xs text-gray-400">申请时间：{{ record.sqsj }}</text>
                <!-- <text class="text-xs text-blue-500">查看详情</text> -->
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 添加分页组件 -->
    </view>

    <view v-if="leaveRecords.length > 0 && totalRecords > pageSize" class="pb-4">
      <Pagination
        :total="totalRecords"
        v-model:page="currentPage"
        :page-size="pageSize"
        @update:page="handlePageChange"
      />
    </view>
    <!-- 请假须知 -->
    <view class="custom-card mb-4 bg-white rounded-lg overflow-hidden">
      <view class="card-header flex justify-between items-center p-3">
        <text class="card-title font-bold text-lg">请假须知</text>
      </view>
      <view class="p-3">
        <view class="text-sm text-gray-700 space-y-2">
          <view class="flex">
            <text class="mr-2">1.</text>
            <text>请假须提前申请，病假可事后补办，但需提供就医证明。</text>
          </view>
          <view class="flex">
            <text class="mr-2">2.</text>
            <text>请假3天以内由辅导员审批，3天以上需系主任审批。</text>
          </view>
          <view class="flex">
            <text class="mr-2">3.</text>
            <text>请假期间若提前返校，须到辅导员处销假。</text>
          </view>
          <view class="flex">
            <text class="mr-2">4.</text>
            <text>一学期内事假累计不得超过7天，病假累计不得超过15天。</text>
          </view>
          <view class="flex">
            <text class="mr-2">5.</text>
            <text>期末考试期间原则上不得请假，特殊情况需提供相关证明。</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加请假按钮 -->
    <view class="fixed right-6 bottom-20">
      <view class="add-button" @click="navigateToApply">
        <wd-icon name="add" color="#ffffff" size="24px" />
      </view>
    </view>

    <!-- 申请请假弹窗 -->
    <wd-popup v-model="isShowApplyDialog" position="bottom" :style="{ height: '90%' }">
      <view class="p-4">
        <view class="flex justify-between items-center mb-4">
          <text class="text-lg font-bold">申请请假</text>
          <view class="close-button" @click="isShowApplyDialog = false">
            <wd-icon name="close" color="#999999" size="18px" />
          </view>
        </view>
        <view class="mb-4">
          <text class="block mb-2 text-sm">请假类型</text>
          <select class="ios-input" v-model="applyForm.leaveType">
            <option value="0" disabled>请选择请假类型</option>
            <option v-for="option in leaveTypeOptions" :key="option.value" :value="option.value">
              {{ option.label }}
            </option>
          </select>
        </view>
        <view class="mb-4">
          <text class="block mb-2 text-sm">开始时间</text>
          <input
            type="text"
            class="ios-input"
            v-model="applyForm.leaveStartTime"
            placeholder="请选择开始时间"
            @click="openDatePicker('start')"
          />
        </view>
        <view class="mb-4">
          <text class="block mb-2 text-sm">结束时间</text>
          <input
            type="text"
            class="ios-input"
            v-model="applyForm.leaveEndTime"
            placeholder="请选择结束时间"
            @click="openDatePicker('end')"
          />
        </view>
        <view class="mb-4">
          <text class="block mb-2 text-sm">请假原因</text>
          <textarea
            class="ios-input h-24 resize-none"
            v-model="applyForm.leaveReason"
            placeholder="请输入请假原因"
            :maxlength="100"
          />
          <view class="text-right text-xs text-gray-400 mt-1">
            {{ applyForm.leaveReason?.length || 0 }}/100
          </view>
        </view>
        <view class="mb-4">
          <text class="block mb-2 text-sm">是否需要出校</text>
          <view class="flex">
            <view
              class="flex-1 flex items-center justify-center p-2 border"
              :class="
                applyForm.isApplyForExitCampus === 1
                  ? 'bg-blue-50 border-blue-500'
                  : 'border-gray-300'
              "
              @click="applyForm.isApplyForExitCampus = 1"
            >
              <text
                :class="applyForm.isApplyForExitCampus === 1 ? 'text-blue-500' : 'text-gray-500'"
              >
                是
              </text>
            </view>
            <view
              class="flex-1 flex items-center justify-center p-2 border"
              :class="
                applyForm.isApplyForExitCampus === 0
                  ? 'bg-blue-50 border-blue-500'
                  : 'border-gray-300'
              "
              @click="applyForm.isApplyForExitCampus = 0"
            >
              <text
                :class="applyForm.isApplyForExitCampus === 0 ? 'text-blue-500' : 'text-gray-500'"
              >
                否
              </text>
            </view>
          </view>
        </view>
        <view class="mb-4">
          <text class="block mb-2 text-sm">上传材料（选填）</text>
          <view
            class="border border-gray-300 border-dashed rounded-lg p-4 text-center"
            @click="chooseFile"
          >
            <wd-icon name="upload" color="#999999" size="24px" class="mb-2" />
            <text class="text-sm text-gray-500 block">点击上传相关证明材料</text>
            <view class="flex flex-wrap mt-2" v-if="uploadFiles.length > 0">
              <view class="upload-file-item" v-for="(file, index) in uploadFiles" :key="index">
                <text class="text-xs">{{ file.name || '文件' + (index + 1) }}</text>
                <wd-icon
                  name="close"
                  color="#f56c6c"
                  size="12px"
                  class="ml-1"
                  @click.stop="removeFile(index)"
                />
              </view>
            </view>
          </view>
        </view>
        <view class="flex space-x-3 mt-8">
          <button class="ios-button secondary flex-1" @click="isShowApplyDialog = false">
            取消
          </button>
          <button class="ios-button flex-1" @click="submitLeaveApplication">提交申请</button>
        </view>
      </view>
    </wd-popup>
  </view>
</template>

<script lang="ts" setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { onShow } from '@dcloudio/uni-app'
import {
  getStudentLeaveRecordsNew,
  submitLeaveApplication as submitLeaveAPI,
} from '@/service/leave'
import type { LeaveRecordItem, LeaveQueryParams, LeaveTypeOption } from '@/types/leave'
import Pagination from '@/components/Pagination/index.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import { getWorkflowTask } from '@/service/workflow'
import { loadDictData, getDictLabel, getDictClass } from '@/utils/dict'
import type { DictData } from '@/types/system'

// 请假记录列表
const leaveRecords = ref<LeaveRecordItem[]>([])
// 请假统计数据
const leaveStatistics = ref<any>({})
// 总记录数
const totalRecords = ref(0)
// 当前页数
const currentPage = ref(1)
// 每页记录数
const pageSize = ref(10)
// 是否正在加载
const loading = ref(false)
// 是否有未销假的请假申请
const hasUnfinishedLeave = ref(false)
// 未销假的请假信息
const unfinishedLeaveInfo = ref<LeaveRecordItem | null>(null)

// 字典数据
const dictData = ref<Record<string, DictData[]>>({})
const leaveCategoryDict = ref<DictData[]>([])
const leaveTypeDict = ref<DictData[]>([])
const approvalStatusDict = ref<DictData[]>([])

// 加载字典数据
const loadLeaveDictData = async () => {
  try {
    const dicts = await loadDictData(['DM_XSQJLB', 'DM_XSQJDM', 'DM_SPZT2'])
    dictData.value = dicts
    leaveCategoryDict.value = dicts.DM_XSQJLB || []
    leaveTypeDict.value = dicts.DM_XSQJDM || []
    approvalStatusDict.value = dicts.DM_SPZT2 || []

    // 更新请假类型选项
    updateLeaveTypeOptions()
  } catch (error) {
    console.error('加载字典数据失败:', error)
  }
}

// 更新请假类型选项
const updateLeaveTypeOptions = () => {
  if (leaveTypeDict.value.length > 0) {
    leaveTypeOptions.value = leaveTypeDict.value.map((item) => ({
      value: parseInt(item.dictValue),
      label: item.dictLabel,
      color: item.listClass || 'blue',
    }))
  }
}

// 请假状态选择
const leaveStatus = ref('')

// 请假类型选项
const leaveTypeOptions = ref<LeaveTypeOption[]>([
  { value: 1, label: '事假', color: 'blue' },
  { value: 2, label: '病假', color: 'red' },
  { value: 6, label: '公假', color: 'green' },
  { value: 8, label: '集训', color: 'purple' },
  { value: 9, label: '其他', color: 'gray' },
])

// 申请请假弹窗控制
const isShowApplyDialog = ref(false)

// 请假申请表单
const applyForm = ref({
  leaveType: 0,
  leaveStartTime: '',
  leaveEndTime: '',
  leaveReason: '',
  isApplyForExitCampus: 1,
})

// 上传文件列表
const uploadFiles = ref<Array<{ name: string; url: string }>>([])

// 查询参数
const queryParams = reactive<LeaveQueryParams>({
  page: 1,
  pageSize: 10,
  leaveTypeName: [],
  leaveTime: [],
  leaveHours: '',
  leaveReason: '',
  requestTime: [],
})

// 累计请假天数计算
const totalLeaveDays = computed(() => {
  if (!leaveRecords.value.length) return 0
  return leaveRecords.value.reduce((total, record) => total + record.qjss / 24, 0).toFixed(1)
})

// 获取请假记录
const getLeaveRecords = async () => {
  try {
    loading.value = true
    // 更新查询参数中的页码
    queryParams.page = currentPage.value
    queryParams.pageSize = pageSize.value

    const res = await getStudentLeaveRecordsNew(queryParams)
    leaveRecords.value = res.items
    leaveStatistics.value = res.statistics
    totalRecords.value = res.total

    // 保存未销假请假申请的标志和信息
    hasUnfinishedLeave.value = res.flag || false
    unfinishedLeaveInfo.value = res.qjxx || null

    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}

// 处理请假状态选择
const handleStatusChange = (event: any) => {
  leaveStatus.value = event.target.value
  console.log('请假状态变更为：', leaveStatus.value)

  // 根据状态过滤请假记录
  if (leaveStatus.value === '1') {
    // 审批中
    queryParams.leaveTypeName = []
    getLeaveRecords()
  } else if (leaveStatus.value === '2') {
    // 已批准
    queryParams.leaveTypeName = []
    getLeaveRecords()
  } else if (leaveStatus.value === '3') {
    // 已拒绝
    queryParams.leaveTypeName = []
    getLeaveRecords()
  } else {
    // 全部
    queryParams.leaveTypeName = []
    getLeaveRecords()
  }
}

// 处理文件上传变化
const handleUploadChange = (event: any) => {
  uploadFiles.value = event.fileList || []
}

// 选择文件
const chooseFile = () => {
  // 使用uni.chooseImage API
  uni.chooseImage({
    count: 1,
    success: (res) => {
      const tempFilePaths = res.tempFilePaths
      const newFile = {
        name: tempFilePaths[0].substring(tempFilePaths[0].lastIndexOf('/') + 1),
        url: tempFilePaths[0],
      }
      uploadFiles.value = [...uploadFiles.value, newFile]

      uni.showToast({
        title: '已添加文件',
        icon: 'none',
      })
    },
  })
}

// 移除文件
const removeFile = (index: number) => {
  uploadFiles.value.splice(index, 1)
}

// 打开日期选择器
const openDatePicker = (type: 'start' | 'end') => {
  uni.showToast({
    title: '请选择日期时间',
    icon: 'none',
  })

  // 使用uni.showDatePicker API模拟
  // 实际项目中应该使用日期选择器组件
  const now = new Date()
  const dateStr = `${now.getFullYear()}-${String(now.getMonth() + 1).padStart(2, '0')}-${String(now.getDate()).padStart(2, '0')} ${String(now.getHours()).padStart(2, '0')}:${String(now.getMinutes()).padStart(2, '0')}`

  if (type === 'start') {
    applyForm.value.leaveStartTime = dateStr
  } else {
    applyForm.value.leaveEndTime = dateStr
  }
}

// 查看请假详情
const viewLeaveDetail = async (id: number) => {
  try {
    console.log('查看请假详情', id)

    // 显示加载提示
    uni.showLoading({
      title: '加载中...',
      mask: true,
    })

    // 调用API获取任务ID
    const response = await getWorkflowTask({
      id,
      code: 'xsqjsq', // 请假申请工作流编码
    })

    // 关闭加载提示
    uni.hideLoading()

    // 检查响应数据
    // 跳转到工作流详情页面
    uni.navigateTo({
      url: `/pages/workflow/detail?id=${response.id}`,
      fail: (err) => {
        console.error('跳转工作流详情页失败:', err)
        uni.showToast({
          title: '跳转工作流详情页失败',
          icon: 'none',
        })
      },
    })
  } catch (error) {
    console.error('获取工作流任务失败:', error)
    uni.hideLoading()
  }
}

// 显示申请请假弹窗
const showApplyLeaveDialog = () => {
  uni.navigateTo({
    url: '/pages/student/leave/apply',
  })
}

// 跳转到请假申请页面
const navigateToApply = () => {
  // 检查是否有未销假的请假申请
  if (hasUnfinishedLeave.value && unfinishedLeaveInfo.value) {
    const leaveInfo = unfinishedLeaveInfo.value
    // 获取请假类型名称
    const leaveTypeName = getLeaveTypeLabel(leaveInfo.qjlx) || leaveInfo.qjlxmc || '请假'
    // 格式化时间显示
    const startTime = leaveInfo.qjkssj || ''
    const endTime = leaveInfo.qjjssj || ''

    // 显示提示信息
    uni.showToast({
      title: `您发起的(${leaveTypeName})${startTime}至${endTime}请假申请还未销假，请处理后再进行请假申请！`,
      icon: 'none',
      duration: 3000,
      mask: true,
    })
    return
  }

  // 如果没有未销假的申请，正常跳转到申请页面
  uni.navigateTo({
    url: '/pages/student/leave/apply',
  })
}

// 提交请假申请
const submitLeaveApplication = async () => {
  // 表单验证
  if (!applyForm.value.leaveType) {
    uni.showToast({
      title: '请选择请假类型',
      icon: 'none',
    })
    return
  }

  if (!applyForm.value.leaveStartTime) {
    uni.showToast({
      title: '请选择开始时间',
      icon: 'none',
    })
    return
  }

  if (!applyForm.value.leaveEndTime) {
    uni.showToast({
      title: '请选择结束时间',
      icon: 'none',
    })
    return
  }

  if (!applyForm.value.leaveReason) {
    uni.showToast({
      title: '请输入请假原因',
      icon: 'none',
    })
    return
  }

  try {
    // 组装附件列表
    const attachmentList = uploadFiles.value.map((file) => file.url).join(',')

    await submitLeaveAPI({
      ...applyForm.value,
      attachmentList: attachmentList ? [attachmentList] : undefined,
    })

    uni.showToast({
      title: '提交成功',
      icon: 'success',
    })

    isShowApplyDialog.value = false

    // 重置表单
    applyForm.value = {
      leaveType: 0,
      leaveStartTime: '',
      leaveEndTime: '',
      leaveReason: '',
      isApplyForExitCampus: 1,
    }
    uploadFiles.value = []

    // 重新获取请假记录
    getLeaveRecords()
  } catch (error) {
    console.error(error)
  }
}

// 处理请假类型选择
const handleTypeConfirm = (value: { value: number }) => {
  applyForm.value.leaveType = value.value
}

// 获取请假类型标签
const getLeaveTypeLabel = (value: string): string => {
  return getDictLabel(leaveTypeDict.value, value)
}

// 获取请假类型样式类
const getLeaveTypeClass = (value: string): string => {
  return getDictClass(leaveTypeDict.value, value)
}

// 获取请假类别标签
const getLeaveCategoryLabel = (value: string | number): string => {
  return getDictLabel(leaveCategoryDict.value, String(value))
}

// 获取请假类别样式类
const getLeaveCategoryClass = (value: string | number): string => {
  return getDictClass(leaveCategoryDict.value, String(value))
}

// 获取请假类别颜色
const getLeaveCategoryColor = (value: string | number): string => {
  const stringValue = String(value)
  // 根据请假类别值查找对应的字典项
  const dictItem = leaveCategoryDict.value.find((item) => item.dictValue === stringValue)

  if (dictItem && dictItem.listClass) {
    // 如果字典中有颜色配置，使用字典配置
    return dictItem.listClass
  }

  // 否则使用默认颜色映射
  const colorMap: Record<string, string> = {
    '0': '#6366f1', // 学习请假 - indigo-500
    '1': '#8b5cf6', // 晚寝请假 - purple-500
    '2': '#f59e0b', // 全部请假 - amber-500
  }

  return colorMap[stringValue] || '#6b7280' // gray-500 作为默认颜色
}

// 获取审批状态标签
const getApprovalStatusLabel = (value: string): string => {
  return getDictLabel(approvalStatusDict.value, value)
}

// 获取审批状态样式类
const getApprovalStatusClass = (value: string): string => {
  return getDictClass(approvalStatusDict.value, value)
}

// 获取请假类型名称
const getLeaveTypeName = (typeValue: number): string => {
  const option = leaveTypeOptions.value.find((item) => item.value === typeValue)
  return option ? option.label : '未知'
}

// 获取请假类型颜色
const getLeaveTypeColor = (typeName: string): string => {
  if (typeName === '事假') return 'blue'
  if (typeName === '病假') return 'red'
  if (typeName === '公假') return 'green'
  if (typeName === '集训') return 'purple'
  return 'gray'
}

// 根据字典数据获取请假类型颜色
const getLeaveTypeColorFromDict = (typeName: string): string => {
  // 根据请假类型名称查找对应的字典项
  const dictItem = leaveTypeDict.value.find((item) => item.dictLabel === typeName)

  if (dictItem && dictItem.listClass) {
    // 如果字典中有颜色配置，使用字典配置
    return dictItem.listClass
  }

  // 否则使用默认颜色映射
  const colorMap: Record<string, string> = {
    事假: '#3b82f6', // blue-500
    病假: '#ef4444', // red-500
    公假: '#10b981', // green-500
    实习: '#8b5cf6', // purple-500
    集训: '#f59e0b', // amber-500
  }

  return colorMap[typeName] || '#6b7280' // gray-500 作为默认颜色
}

// 获取请假类型图标
const getLeaveTypeIcon = (typeName: string): string => {
  const iconMap: Record<string, string> = {
    事假: 'user',
    病假: 'medical',
    公假: 'office-building',
    实习: 'briefcase',
    集训: 'trophy',
  }
  return iconMap[typeName] || 'calendar'
}

// 获取请假类型图标颜色
const getLeaveTypeIconColor = (typeName: string): string => {
  return getLeaveTypeColorFromDict(typeName)
}

// 获取请假类型图标背景颜色
const getLeaveTypeIconBgColor = (typeName: string): string => {
  const baseColor = getLeaveTypeColorFromDict(typeName)
  // 将颜色转换为浅色背景
  const colorMap: Record<string, string> = {
    '#3b82f6': '#dbeafe', // blue-50
    '#ef4444': '#fef2f2', // red-50
    '#10b981': '#ecfdf5', // green-50
    '#8b5cf6': '#f3e8ff', // purple-50
    '#f59e0b': '#fffbeb', // amber-50
  }
  return colorMap[baseColor] || '#f9fafb' // gray-50 作为默认背景
}

// 获取审批状态
const getApprovalStatus = (
  record: LeaveRecordItem,
): { status: string; color: 'warning' | 'success' | 'danger' } => {
  // 使用 sp1 字段获取审批状态
  let status = ''
  let color: 'warning' | 'success' | 'danger' = 'warning'

  if (record.sp1 === 0) {
    status = getApprovalStatusLabel('0') || '审批中'
    color = 'warning'
  } else if (record.sp1 === 1) {
    status = getApprovalStatusLabel('1') || '已批准'
    color = 'success'
  } else {
    status = getApprovalStatusLabel('2') || '已拒绝'
    color = 'danger'
  }

  return { status, color }
}

// 处理页码变更
const handlePageChange = (page: number) => {
  currentPage.value = page
  getLeaveRecords()
}

// 监听页码变化
watch(currentPage, () => {
  if (leaveRecords.value.length > 0) {
    getLeaveRecords()
  }
})

// 页面加载时获取请假记录
onMounted(async () => {
  await loadLeaveDictData()
  getLeaveRecords()
})

// 页面每次显示时重新获取数据
onShow(() => {
  console.log('页面显示，重新加载数据')
  getLeaveRecords()
})
</script>

<style>
.leave-page {
  min-height: 100vh;
}

.card-header {
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.card-title {
  position: relative;
  padding-left: 12px;
}

.card-title::before {
  position: absolute;
  top: 50%;
  left: 0;
  width: 4px;
  height: 16px;
  content: '';
  background-color: #3a73f9;
  border-radius: 2px;
  transform: translateY(-50%);
}
/* 添加按钮样式 */
.add-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50px;
  height: 50px;
  color: white;
  cursor: pointer;
  background-color: #3a73f9;
  border-radius: 50%;
  box-shadow: 0 4px 10px rgba(58, 115, 249, 0.3);
}
/* 关闭按钮样式 */
.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 30px;
  height: 30px;
  color: #999;
  cursor: pointer;
}
/* 表单控件样式 */
.ios-input {
  display: block;
  width: 100%;
  padding: 10px;
  font-size: 14px;
  color: #333;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 6px;
}

.ios-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  padding: 10px;
  font-size: 15px;
  font-weight: 500;
  color: white;
  text-align: center;
  background-color: #3a73f9;
  border-radius: 6px;
}

.ios-button.secondary {
  color: #333;
  background-color: #f5f5f5;
}
/* 上传文件项样式 */
.upload-file-item {
  display: flex;
  align-items: center;
  padding: 4px 8px;
  margin-right: 6px;
  margin-bottom: 6px;
  background-color: #f0f0f0;
  border-radius: 4px;
}
/* 加载动画 */
.loading-spinner {
  display: inline-block;
  width: 30px;
  height: 30px;
  border: 3px solid rgba(0, 0, 0, 0.1);
  border-top-color: #3a73f9;
  border-radius: 50%;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style>
